import { create } from 'zustand';
import { User, Role, Permission } from '../types/auth';

interface AuthState {
  // Users
  users: User[];
  selectedUser: User | null;
  userLoading: boolean;
  userError: string | null;
  
  // Roles
  roles: Role[];
  selectedRole: Role | null;
  roleLoading: boolean;
  roleError: string | null;
  
  // Permissions
  availablePermissions: Permission[];
  permissionLoading: boolean;
  permissionError: string | null;
  
  // UI State
  userDialogOpen: boolean;
  roleDialogOpen: boolean;
  deleteConfirmOpen: boolean;
  deleteTarget: { type: 'user' | 'role'; id: number } | null;
  
  // Actions
  setUsers: (users: User[]) => void;
  setSelectedUser: (user: User | null) => void;
  setUserLoading: (loading: boolean) => void;
  setUserError: (error: string | null) => void;
  
  setRoles: (roles: Role[]) => void;
  setSelectedRole: (role: Role | null) => void;
  setRoleLoading: (loading: boolean) => void;
  setRoleError: (error: string | null) => void;
  
  setAvailablePermissions: (permissions: Permission[]) => void;
  setPermissionLoading: (loading: boolean) => void;
  setPermissionError: (error: string | null) => void;
  
  setUserDialogOpen: (open: boolean) => void;
  setRoleDialogOpen: (open: boolean) => void;
  setDeleteConfirmOpen: (open: boolean) => void;
  setDeleteTarget: (target: { type: 'user' | 'role'; id: number } | null) => void;
  
  addUser: (user: User) => void;
  updateUser: (user: User) => void;
  removeUser: (id: number) => void;
  
  addRole: (role: Role) => void;
  updateRole: (role: Role) => void;
  removeRole: (id: number) => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  // Initial state
  users: [],
  selectedUser: null,
  userLoading: false,
  userError: null,
  
  roles: [],
  selectedRole: null,
  roleLoading: false,
  roleError: null,
  
  availablePermissions: [],
  permissionLoading: false,
  permissionError: null,
  
  userDialogOpen: false,
  roleDialogOpen: false,
  deleteConfirmOpen: false,
  deleteTarget: null,
  
  // Actions
  setUsers: (users) => set({ users }),
  setSelectedUser: (user) => set({ selectedUser: user }),
  setUserLoading: (loading) => set({ userLoading: loading }),
  setUserError: (error) => set({ userError: error }),
  
  setRoles: (roles) => set({ roles }),
  setSelectedRole: (role) => set({ selectedRole: role }),
  setRoleLoading: (loading) => set({ roleLoading: loading }),
  setRoleError: (error) => set({ roleError: error }),
  
  setAvailablePermissions: (permissions) => set({ availablePermissions: permissions }),
  setPermissionLoading: (loading) => set({ permissionLoading: loading }),
  setPermissionError: (error) => set({ permissionError: error }),
  
  setUserDialogOpen: (open) => set({ userDialogOpen: open }),
  setRoleDialogOpen: (open) => set({ roleDialogOpen: open }),
  setDeleteConfirmOpen: (open) => set({ deleteConfirmOpen: open }),
  setDeleteTarget: (target) => set({ deleteTarget: target }),
  
  addUser: (user) => set((state) => ({ users: [...state.users, user] })),
  updateUser: (user) => set((state) => ({
    users: state.users.map(u => u.id === user.id ? user : u)
  })),
  removeUser: (id) => set((state) => ({
    users: state.users.filter(u => u.id !== id)
  })),
  
  addRole: (role) => set((state) => ({ roles: [...state.roles, role] })),
  updateRole: (role) => set((state) => ({
    roles: state.roles.map(r => r.id === role.id ? role : r)
  })),
  removeRole: (id) => set((state) => ({
    roles: state.roles.filter(r => r.id !== id)
  })),
}));
