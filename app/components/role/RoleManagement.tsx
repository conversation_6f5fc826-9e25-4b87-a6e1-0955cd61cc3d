import React, { useState } from 'react';
import { Plus, Edit, Trash2, Users, Shield, Search, X } from 'lucide-react';
import useSWR from 'swr';
import { Role } from '../../types/auth';
import { api, parsePermissions } from '../../lib/api';
import { useAuthStore } from '../../store/auth-store';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Skeleton } from '../ui/skeleton';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { toast } from 'sonner';
import { RoleDialog } from './RoleDialog';
import { DeleteConfirmDialog } from '../shared/DeleteConfirmDialog';
import { TablePagination, calculateTotalPages, paginateData } from '../shared/TablePagination';

export function RoleManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [userCountFilter, setUserCountFilter] = useState<string>('');
  const [permissionFilter, setPermissionFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const {
    roleDialogOpen,
    setRoleDialogOpen,
    selectedRole,
    setSelectedRole,
    deleteConfirmOpen,
    setDeleteConfirmOpen,
    deleteTarget,
    setDeleteTarget
  } = useAuthStore();

  // Fetch roles with SWR
  const { data: roles, error, mutate, isLoading } = useSWR(
    'roles',
    api.getRoles,
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000
    }
  );

  // Fetch users to show role usage
  const { data: usersData } = useSWR(
    'users',
    () => api.getUsers({ page: 1, size: 1000 }),
    { revalidateOnFocus: false }
  );

  // Fetch permissions to calculate actual permission count
  const { data: allPermissions } = useSWR(
    'permissions',
    api.getPermissions,
    { revalidateOnFocus: false }
  );

  const handleCreateRole = () => {
    setSelectedRole(null);
    setRoleDialogOpen(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setRoleDialogOpen(true);
  };

  const handleDeleteRole = (role: Role) => {
    setDeleteTarget({ type: 'role', id: role.id });
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!deleteTarget || deleteTarget.type !== 'role') return;
    
    try {
      await api.deleteRole(deleteTarget.id);
      mutate();
      toast.success('角色删除成功');
      setDeleteConfirmOpen(false);
      setDeleteTarget(null);
    } catch (error: any) {
      toast.error(error.message || '删除失败');
    }
  };

  const getRoleUserCount = (roleId: number) => {
    if (!usersData) return 0;
    return usersData.items.filter(user => 
      user.roles.some(role => role.id === roleId)
    ).length;
  };

  const getRoleUsers = (roleId: number) => {
    if (!usersData) return [];
    return usersData.items.filter(user => 
      user.roles.some(role => role.id === roleId)
    );
  };

  const getPermissionCount = (permissionString: string) => {
    if (!permissionString || !allPermissions) return 0;
    
    const permissionTokens = permissionString.split(',').map(p => p.trim()).filter(Boolean);
    let count = 0;
    
    permissionTokens.forEach(token => {
      const [type, service, module, action] = token.split(':');
      
      if (!type || !service || !module || !action) return;
      
      // Count matching permissions based on wildcards
      if (action === '*' && module === '*') {
        // service level wildcard: api:user:*:*
        count += allPermissions.filter(p => 
          p.type === type && p.service === service
        ).length;
      } else if (action === '*') {
        // module level wildcard: api:content:article:*
        count += allPermissions.filter(p => 
          p.type === type && p.service === service && p.module === module
        ).length;
      } else if (module === '*') {
        // service action wildcard: api:content:*:read
        count += allPermissions.filter(p => 
          p.type === type && p.service === service && p.action === action
        ).length;
      } else {
        // exact permission: api:user:profile:read
        const exists = allPermissions.some(p => 
          p.type === type && p.service === service && 
          p.module === module && p.action === action
        );
        if (exists) count += 1;
      }
    });
    
    return count;
  };

  // Filter and search functions
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handleUserCountFilter = (value: string) => {
    setUserCountFilter(value);
  };

  const handlePermissionFilter = (value: string) => {
    setPermissionFilter(value);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setUserCountFilter('');
    setPermissionFilter('');
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // Filter roles based on search and filters
  const allFilteredRoles = roles?.filter(role => {
    const userCount = getRoleUserCount(role.id);
    const permissionCount = getPermissionCount(role.permissions);

    // Text search - check name, description, and permission content
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      const matchesName = role.name.toLowerCase().includes(search);
      const matchesDescription = role.description.toLowerCase().includes(search);
      const matchesPermissions = role.permissions.toLowerCase().includes(search);

      // Also search in parsed permission details
      let matchesPermissionDetails = false;
      if (allPermissions) {
        const rolePermissions = parsePermissions(role.permissions);
        matchesPermissionDetails = rolePermissions.some(perm =>
          perm.name?.toLowerCase().includes(search) ||
          perm.description?.toLowerCase().includes(search) ||
          perm.service.toLowerCase().includes(search) ||
          perm.module.toLowerCase().includes(search) ||
          perm.action.toLowerCase().includes(search)
        );
      }

      if (!matchesName && !matchesDescription && !matchesPermissions && !matchesPermissionDetails) {
        return false;
      }
    }

    // User count filter
    if (userCountFilter && userCountFilter !== 'all') {
      switch (userCountFilter) {
        case 'none':
          if (userCount > 0) return false;
          break;
        case 'some':
          if (userCount === 0) return false;
          break;
        case 'many':
          if (userCount < 5) return false;
          break;
      }
    }

    // Permission count filter
    if (permissionFilter && permissionFilter !== 'all') {
      switch (permissionFilter) {
        case 'few':
          if (permissionCount > 5) return false;
          break;
        case 'medium':
          if (permissionCount <= 5 || permissionCount > 15) return false;
          break;
        case 'many':
          if (permissionCount <= 15) return false;
          break;
      }
    }

    return true;
  }) || [];

  // Apply pagination to filtered results
  const filteredRoles = paginateData(allFilteredRoles, currentPage, pageSize);
  const totalPages = calculateTotalPages(allFilteredRoles.length, pageSize);

  // Check if any filters are applied
  const hasActiveFilters = searchTerm || userCountFilter || permissionFilter;

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, userCountFilter, permissionFilter]);

  if (error) {
    return (
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="text-center text-destructive">
            加载失败：{error.message}
          </div>
        </CardContent>
      </Card>
    );
  }
